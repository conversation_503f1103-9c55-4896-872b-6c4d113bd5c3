{"data_mtime": 1758122933, "dep_lines": [18, 23, 10, 11, 12, 13, 14, 15, 16, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 10, 5, 30, 30], "dependencies": ["simulated_devices.wire_protocol_simulators", "simulated_devices.protocol_communication", "asyncio", "logging", "datetime", "typing", "enum", "dataclasses", "json", "builtins", "_frozen_importlib", "abc"], "hash": "bdcf4aa40ec42a8b67963049ae0bcbaa6f84bdaf", "id": "simulated_devices.unified_protocol_server", "ignore_all": true, "interface_hash": "f93a39ac9a22b71d8076ccf16e00713ebc017c20", "mtime": 1758065442, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\Quanta\\simulated_devices\\unified_protocol_server.py", "plugin_data": null, "size": 22955, "suppressed": [], "version_id": "1.15.0"}