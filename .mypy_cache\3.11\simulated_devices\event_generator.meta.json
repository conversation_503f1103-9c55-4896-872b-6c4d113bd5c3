{"data_mtime": 1757981498, "dep_lines": [13, 6, 7, 8, 9, 10, 11, 12, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["simulated_devices.power_system_simulator", "asyncio", "random", "logging", "datetime", "typing", "dataclasses", "enum", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "abc", "asyncio.exceptions"], "hash": "97a43378890d5c0032cc8d876ed1129ffcd5c818", "id": "simulated_devices.event_generator", "ignore_all": true, "interface_hash": "d4f14034bab833634fbb341a824b89f4250e7fa7", "mtime": 1757981395, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\Quanta\\simulated_devices\\event_generator.py", "plugin_data": null, "size": 18018, "suppressed": [], "version_id": "1.15.0"}