2025-09-17 13:07:06,384 - DeviceFactory - INFO - Loaded 8 device profiles
2025-09-17 13:07:06,386 - EventGenerator - INFO - Loaded 8 default event scenarios
2025-09-17 13:07:06,387 - DeviceFactory - INFO - Loaded 8 device profiles
2025-09-17 13:07:06,392 - ProtocolTestingDemo - INFO - === Starting Comprehensive Protocol Testing ===
2025-09-17 13:07:06,392 - ProtocolTestingDemo - INFO - Setting up test devices...
2025-09-17 13:07:06,392 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_DNP3_RELAY - INFO - Initialized protective_relay simulator: PROTOCOL_TEST_SUBSTATION_DNP3_RELAY
2025-09-17 13:07:06,393 - DeviceFactory - INFO - Created Schweitzer Engineering Laboratories SEL-351 simulator: PROTOCOL_TEST_SUBSTATION_DNP3_RELAY
2025-09-17 13:07:06,393 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_MODBUS_METER - INFO - Initialized power_meter simulator: PROTOCOL_TEST_SUBSTATION_MODBUS_METER
2025-09-17 13:07:06,393 - DeviceFactory - INFO - Created Schneider Electric PowerLogic PM8000 simulator: PROTOCOL_TEST_SUBSTATION_MODBUS_METER
2025-09-17 13:07:06,393 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - INFO - Initialized phasor_measurement_unit simulator: PROTOCOL_TEST_SUBSTATION_IEC61850_PMU
2025-09-17 13:07:06,393 - DeviceFactory - INFO - Created Arbiter Systems 1133A simulator: PROTOCOL_TEST_SUBSTATION_IEC61850_PMU
2025-09-17 13:07:06,393 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_MULTI_PROTOCOL_DFR - INFO - Initialized digital_fault_recorder simulator: PROTOCOL_TEST_SUBSTATION_MULTI_PROTOCOL_DFR
2025-09-17 13:07:06,395 - DeviceFactory - INFO - Created Qualitrol DFR-1800 simulator: PROTOCOL_TEST_SUBSTATION_MULTI_PROTOCOL_DFR
2025-09-17 13:07:06,395 - DeviceFactory - INFO - Created substation 'PROTOCOL_TEST_SUBSTATION' with 4 devices
2025-09-17 13:07:06,395 - SimulationCoordinator - INFO - Registered device: PROTOCOL_TEST_SUBSTATION_DNP3_RELAY
2025-09-17 13:07:06,395 - SimulationCoordinator - INFO - Registered device: PROTOCOL_TEST_SUBSTATION_MODBUS_METER
2025-09-17 13:07:06,395 - SimulationCoordinator - INFO - Registered device: PROTOCOL_TEST_SUBSTATION_IEC61850_PMU
2025-09-17 13:07:06,395 - SimulationCoordinator - INFO - Registered device: PROTOCOL_TEST_SUBSTATION_MULTI_PROTOCOL_DFR
2025-09-17 13:07:06,396 - ProtocolTestingDemo - INFO - Test devices setup complete
2025-09-17 13:07:06,396 - SimulationCoordinator - INFO - Starting simulation with 4 devices
2025-09-17 13:07:06,396 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_DNP3_RELAY - INFO - Starting protective_relay simulation
2025-09-17 13:07:06,397 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_MODBUS_METER - INFO - Starting power_meter simulation
2025-09-17 13:07:06,397 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - INFO - Starting phasor_measurement_unit simulation
2025-09-17 13:07:06,397 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_MULTI_PROTOCOL_DFR - INFO - Starting digital_fault_recorder simulation
2025-09-17 13:07:06,397 - EventGenerator - INFO - Starting automatic event generation
2025-09-17 13:07:06,399 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_DNP3_RELAY - ERROR - Relay protection logic error: 'PowerSystemSimulator' object has no attribute '_evaluate_protection_elements'
2025-09-17 13:07:06,399 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - ERROR - Measurement generation error: invalid literal for int() with base 10: 'PMU'
2025-09-17 13:07:06,399 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - ERROR - PMU synchrophasor streaming error: 'PowerSystemSimulator' object has no attribute '_generate_synchrophasor_frame'
2025-09-17 13:07:07,411 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - ERROR - Measurement generation error: invalid literal for int() with base 10: 'PMU'
2025-09-17 13:07:07,412 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_DNP3_RELAY - ERROR - Relay protection logic error: 'PowerSystemSimulator' object has no attribute '_evaluate_protection_elements'
2025-09-17 13:07:07,412 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - ERROR - PMU synchrophasor streaming error: 'PowerSystemSimulator' object has no attribute '_generate_synchrophasor_frame'
2025-09-17 13:07:08,411 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_DNP3_RELAY - ERROR - Relay protection logic error: 'PowerSystemSimulator' object has no attribute '_evaluate_protection_elements'
2025-09-17 13:07:08,411 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - ERROR - PMU synchrophasor streaming error: 'PowerSystemSimulator' object has no attribute '_generate_synchrophasor_frame'
2025-09-17 13:07:08,412 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - ERROR - Measurement generation error: invalid literal for int() with base 10: 'PMU'
2025-09-17 13:07:09,424 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - ERROR - Measurement generation error: invalid literal for int() with base 10: 'PMU'
2025-09-17 13:07:09,424 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_DNP3_RELAY - ERROR - Relay protection logic error: 'PowerSystemSimulator' object has no attribute '_evaluate_protection_elements'
2025-09-17 13:07:09,425 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - ERROR - PMU synchrophasor streaming error: 'PowerSystemSimulator' object has no attribute '_generate_synchrophasor_frame'
2025-09-17 13:07:10,445 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_DNP3_RELAY - ERROR - Relay protection logic error: 'PowerSystemSimulator' object has no attribute '_evaluate_protection_elements'
2025-09-17 13:07:10,445 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - ERROR - Measurement generation error: invalid literal for int() with base 10: 'PMU'
2025-09-17 13:07:10,446 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - ERROR - PMU synchrophasor streaming error: 'PowerSystemSimulator' object has no attribute '_generate_synchrophasor_frame'
2025-09-17 13:07:11,398 - ProtocolTestingDemo - INFO - === Testing Protocol Communication ===
2025-09-17 13:07:11,399 - ProtocolTestingDemo - INFO - Testing DNP3 communication...
2025-09-17 13:07:11,399 - ProtocolTestingDemo - INFO - Testing Modbus communication...
2025-09-17 13:07:11,399 - ProtocolTestingDemo - INFO - Testing IEC 61850 communication...
2025-09-17 13:07:11,400 - ProtocolTestingDemo - INFO - === Testing File Transfer Protocols ===
2025-09-17 13:07:11,400 - HTTPServer - INFO - Starting HTTP server on port 8080
2025-09-17 13:07:11,440 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - ERROR - Measurement generation error: invalid literal for int() with base 10: 'PMU'
2025-09-17 13:07:11,440 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - ERROR - PMU synchrophasor streaming error: 'PowerSystemSimulator' object has no attribute '_generate_synchrophasor_frame'
2025-09-17 13:07:11,441 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_DNP3_RELAY - ERROR - Relay protection logic error: 'PowerSystemSimulator' object has no attribute '_evaluate_protection_elements'
2025-09-17 13:07:11,443 - FileTransferCoordinator - ERROR - Failed to start HTTP server: [Errno 10048] error while attempting to bind on address ('::1', 8080, 0, 0): only one usage of each socket address (protocol/network address/port) is normally permitted
2025-09-17 13:07:11,444 - FTPServer - INFO - Starting FTP server on port 2121
2025-09-17 13:07:11,446 - FileTransferCoordinator - INFO - Started FTP server
2025-09-17 13:07:11,447 - SFTPServer - INFO - Starting SFTP server on port 2222
2025-09-17 13:07:11,462 - FTPServer - INFO - FTP server listening on port 2121
2025-09-17 13:07:11,464 - FileTransferCoordinator - INFO - Started SFTP server
2025-09-17 13:07:11,466 - SFTPServer - INFO - SFTP server listening on port 2222
2025-09-17 13:07:12,439 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - ERROR - Measurement generation error: invalid literal for int() with base 10: 'PMU'
2025-09-17 13:07:12,439 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - ERROR - PMU synchrophasor streaming error: 'PowerSystemSimulator' object has no attribute '_generate_synchrophasor_frame'
2025-09-17 13:07:12,439 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_DNP3_RELAY - ERROR - Relay protection logic error: 'PowerSystemSimulator' object has no attribute '_evaluate_protection_elements'
2025-09-17 13:07:13,427 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_DNP3_RELAY - ERROR - Relay protection logic error: 'PowerSystemSimulator' object has no attribute '_evaluate_protection_elements'
2025-09-17 13:07:13,428 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - ERROR - Measurement generation error: invalid literal for int() with base 10: 'PMU'
2025-09-17 13:07:13,428 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - ERROR - PMU synchrophasor streaming error: 'PowerSystemSimulator' object has no attribute '_generate_synchrophasor_frame'
2025-09-17 13:07:15,621 - HTTPServer - INFO - Stopped HTTP server
2025-09-17 13:07:15,622 - FileTransferCoordinator - INFO - Stopped HTTP server
2025-09-17 13:07:15,622 - FTPServer - INFO - Stopped FTP server
2025-09-17 13:07:15,622 - FileTransferCoordinator - INFO - Stopped FTP server
2025-09-17 13:07:15,622 - SFTPServer - INFO - Stopped SFTP server
2025-09-17 13:07:15,622 - FileTransferCoordinator - INFO - Stopped SFTP server
2025-09-17 13:07:15,623 - ProtocolTestingDemo - INFO - === Testing Communication Error Simulation ===
2025-09-17 13:07:15,623 - ProtocolTestingDemo - INFO - === Testing Protocol Timing and Latency ===
2025-09-17 13:07:15,623 - ProtocolTestingDemo - ERROR - Protocol testing error: module 'random' has no attribute 'exponential'
2025-09-17 13:07:15,625 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_DNP3_RELAY - INFO - Simulation tasks cancelled
2025-09-17 13:07:15,626 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_IEC61850_PMU - INFO - Simulation tasks cancelled
2025-09-17 13:07:15,626 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_MODBUS_METER - INFO - Simulation tasks cancelled
2025-09-17 13:07:15,627 - PowerSimulator.PROTOCOL_TEST_SUBSTATION_MULTI_PROTOCOL_DFR - INFO - Simulation tasks cancelled
2025-09-17 13:07:15,627 - EventGenerator - INFO - Event generation cancelled
2025-09-17 13:07:15,627 - SimulationCoordinator - INFO - Simulation cancelled
