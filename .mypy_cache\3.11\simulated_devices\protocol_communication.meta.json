{"data_mtime": 1758049438, "dep_lines": [16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["asyncio", "logging", "struct", "random", "time", "<PERSON><PERSON><PERSON>", "base64", "json", "ssl", "uuid", "datetime", "typing", "enum", "pathlib", "socket", "dataclasses", "aiohttp", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "os", "typing_extensions"], "hash": "6a87943cfc9ea7d0236399160dc77a39e1024f23", "id": "simulated_devices.protocol_communication", "ignore_all": true, "interface_hash": "1ab90ca1cf2bc156c957f0efe11ada829ac37d20", "mtime": 1758049369, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\Quanta\\simulated_devices\\protocol_communication.py", "plugin_data": null, "size": 30593, "suppressed": [], "version_id": "1.15.0"}